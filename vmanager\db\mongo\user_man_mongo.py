import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import DuplicateKeyError
from vmanager import DB_URL


client = AsyncIOMotorClient(DB_URL)
db = client["vmanager"]
users_collection = db["usermanagement"]

INSERTION_LOCK = asyncio.Lock()


async def is_user_authorized(user_id: int) -> bool:
    user = await users_collection.find_one({"user_id": user_id})
    return user is not None and user.get("authorized", False)


async def add_auth_user(user_id: int, username: str = None, first_name: str = None):
    await users_collection.update_one(
        {"user_id": user_id},
        {
            "$set": {
                "user_id": user_id,
                "username": username,
                "first_name": first_name,
                "authorized": False,
                "joined_at": asyncio.get_event_loop().time(),
                "status": "on",
                "mod": False,
                "session_mod": False,
                # Command authorization fields
                "commands": {
                    "listchannels": False,
                    "joinchannel": False,
                    "leavechannel": False,
                    "addupview": False,
                    "rmupview": False,
                    "addupreaction": False,
                    "rmupreaction": False,
                    "react": False,
                    "view": False,
                    "msg": False,
                    "photo": False,
                    "video": False
                }
            }
        },
        upsert=True,
    )


async def get_all_users():
    cursor = users_collection.find()
    users = await cursor.to_list()
    return users


async def authorize_user(user_id: int) -> bool:
    result = await users_collection.update_one(
        {"user_id": user_id}, {"$set": {"authorized": True}}
    )
    return result.modified_count > 0


async def add_mod(user_id: int) -> bool:
    result = await users_collection.update_one(
        {"user_id": user_id}, {"$set": {"mod": True}}
    )
    return result.modified_count > 0


async def remove_mod(user_id: int) -> bool:
    result = await users_collection.update_one(
        {"user_id": user_id}, {"$set": {"mod": False}}
    )
    return result.modified_count > 0


async def add_session_mod(user_id: int):
    result = await users_collection.update_one(
        {"user_id": user_id}, {"$set": {"session_mod": True}}
    )
    return result.modified_count > 0


async def remove_session_mod(user_id: int) -> bool:
    result = await users_collection.update_one(
        {"user_id": user_id}, {"$set": {"session_mod": False}}
    )
    return result.modified_count > 0


async def is_user_on(user_id: int) -> bool:
    user = await users_collection.find_one({"user_id": user_id})
    return user is not None and user.get("status", "on") == "on"


async def user_off(user_id: int):
    await users_collection.update_one({"user_id": user_id}, {"$set": {"status": "off"}})


async def user_on(user_id: int):
    await users_collection.update_one({"user_id": user_id}, {"$set": {"status": "on"}})


async def remove_auth_user(user_id: int) -> bool:
    result = await users_collection.delete_one({"user_id": user_id})
    return result.deleted_count > 0


async def get_mod_users():
    cursor = users_collection.find({"mod": True})
    users = await cursor.to_list(length=None)
    return [user["user_id"] for user in users]


async def get_session_mod_users():
    cursor = users_collection.find({"session_mod": True})
    users = await cursor.to_list(length=None)
    return [user["user_id"] for user in users]


async def get_authorized_users():
    cursor = users_collection.find({"authorized": True})
    users = await cursor.to_list(length=None)
    return [user["user_id"] for user in users]


async def is_command_authorized(user_id: int, command: str) -> bool:
    user = await users_collection.find_one({"user_id": user_id})
    if not user:
        return False
    return user.get("commands", {}).get(command, False)


async def authorize_command(user_id: int, command: str) -> bool:
    result = await users_collection.update_one(
        {"user_id": user_id},
        {"$set": {f"commands.{command}": True}}
    )
    return result.modified_count > 0


async def revoke_command(user_id: int, command: str) -> bool:
    result = await users_collection.update_one(
        {"user_id": user_id},
        {"$set": {f"commands.{command}": False}}
    )
    return result.modified_count > 0


async def get_user_commands(user_id: int):
    user = await users_collection.find_one({"user_id": user_id})
    if not user:
        return {}
    return user.get("commands", {})
