import asyncio
import random
from typing import Dict, Optional
import traceback
from pyrogram import Client, filters
from pyrogram.types import Message, ReactionTypeEmoji
from pyrogram.errors import (
    AuthKeyUnregistered,
    AuthKeyInvalid,
    SessionExpired,
    UserDeactivated,
    AuthKeyDuplicated,
    UserAlreadyParticipant,
    UserNotParticipant,
    ChannelPrivate,
    PeerIdInvalid
)
from vmanager import ADMINS, APP_ID, API_HASH, LOGGER
from vmanager.utils.constants import DEF_REC
from vmanager.db import (
    add_channel,
    remove_channel,
    get_authorized_users,
    get_upcoming_views,
    get_upcoming_reactions,
    get_all_sessions,
    get_session,
    is_account_exists,
    get_all_channel_ids,
    delete_session_by_string,
    get_all_users,
    get_user_commands,
    revoke_command,
    remove_mod,
    remove_session_mod
)
# from vmanager.clients import user_instance, 
from __main__ import user as user_instance
from vmanager.plugins.command_auth import check_command_auth
import os


active_clients: Dict[str, Client] = {}


async def start_client(
    session_string: str, account_id: str
) -> tuple[bool, Optional[Client], Optional[str]]:
    try:
        client = Client(
            f"user_{account_id}",
            api_id=APP_ID,
            api_hash=API_HASH,
            session_string=session_string,
            in_memory=True,
        )
        await client.start()

        active_clients[account_id] = client
        # me = await client.get_me()
        # LOGGER.info(
        #     f"Started client for {me.first_name} (@{me.username or 'no username'}) with ID {me.id}"
        # )
        return True, client, None
    except (
        AuthKeyUnregistered,
        AuthKeyInvalid,
        SessionExpired,
        UserDeactivated,
        AuthKeyDuplicated,
    ) as e:
        await delete_session_by_string(session_string)
        return False, None, f"Session invalid: {str(e)}"
    except Exception as e:
        error_msg = f"Failed to start client: {str(e)}"
        LOGGER.error(error_msg)
        LOGGER.error(traceback.format_exc())
        return False, None, error_msg


async def stop_client(account_id: str) -> bool:
    try:
        if account_id in active_clients:
            client = active_clients[account_id]
            await client.stop()
            del active_clients[account_id]
            LOGGER.info(f"Stopped client for account {account_id}")
            return True
        else:
            LOGGER.warning(f"No active client found for account {account_id}")
            return False
    except Exception as e:
        LOGGER.error(f"Error stopping client for account {account_id}: {str(e)}")
        LOGGER.error(traceback.format_exc())
        return False


async def stop_all_clients() -> tuple[int, int]:
    success_count = 0
    failed_count = 0

    for account_id in list(active_clients.keys()):
        if await stop_client(account_id):
            success_count += 1
        else:
            failed_count += 1

    return success_count, failed_count


async def get_client(account_id: str) -> Optional[Client]:
    if account_id in active_clients:
        return active_clients[account_id]

    session = await get_session(account_id)
    if not session:
        LOGGER.error(f"No session found for account {account_id}")
        return None

    session_string = session.get("session_string")
    if not session_string:
        LOGGER.error(f"No session string found for account {account_id}")
        return None

    success, client, error = await start_client(session_string, account_id)
    if success and client:
        return client
    else:
        LOGGER.error(f"Failed to start client for account {account_id}: {error}")
        return None


@Client.on_message(filters.command(["startall"]) & filters.user(ADMINS))
async def start_all_clients(client: Client, message: Message):
    status_msg = await message.reply_text("Starting all clients...", quote=True)

    sessions = await get_all_sessions()
    sessions = [s for s in sessions if s.get("details_populated", False)]

    if not sessions:
        await status_msg.edit_text("No populated sessions found in the database!")
        return

    await status_msg.edit_text(f"Found {len(sessions)} sessions. Starting clients...")

    success_count = 0
    failed_count = 0
    error_details = []

    for session in sessions:
        account_id = session.get("account_id")
        session_string = session.get("session_string")
        name = session.get("name", "Unknown")

        if account_id in active_clients:
            success_count += 1
            continue

        success, _, error = await start_client(session_string, account_id)

        if success:
            success_count += 1
        else:
            failed_count += 1
            error_details.append(f"- {name} ({account_id}): {error}")

        if (success_count + failed_count) % 5 == 0 or (
            success_count + failed_count
        ) == len(sessions):
            await status_msg.edit_text(
                f"Starting clients... ({success_count + failed_count}/{len(sessions)})\n"
                f"✅ Success: {success_count}\n"
                f"❌ Failed: {failed_count}"
            )

    report = "**Client Startup Complete**\n\n"
    report += f"✅ **Success:** `{success_count}`\n"
    report += f"❌ **Failed:** `{failed_count}`\n"
    report += f"🔄 **Total Active:** `{len(active_clients)}`\n\n"

    if error_details:
        report += "**Errors:**\n"
        for i, error in enumerate(error_details[:10], 1):
            report += f"{i}. {error}\n"

        if len(error_details) > 10:
            report += f"... and {len(error_details) - 10} more errors"

    await status_msg.edit_text(report)


@Client.on_message(filters.command(["stopall"]) & filters.user(ADMINS))
async def stop_all_clients_command(client: Client, message: Message):
    status_msg = await message.reply_text(
        f"Stopping all {len(active_clients)} active clients...", quote=True
    )

    if not active_clients:
        await status_msg.edit_text("No active clients to stop!")
        return

    success_count, failed_count = await stop_all_clients()

    await status_msg.edit_text(
        f"**Client Shutdown Complete**\n\n"
        f"✅ **Successfully stopped:** `{success_count}`\n"
        f"❌ **Failed to stop:** `{failed_count}`\n"
        f"🔄 **Remaining active:** `{len(active_clients)}`"
    )


@Client.on_message(filters.command(["startclient"]) & filters.user(ADMINS))
async def start_specific_client(client: Client, message: Message):
    if len(message.command) < 2:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/startclient account_id`\n"
            "**Example:** `/startclient *********`",
            quote=True,
        )
        return

    account_id = message.command[1]

    if not await is_account_exists(account_id):
        await message.reply_text(
            f"No session found for account ID `{account_id}`!", quote=True
        )
        return

    if account_id in active_clients:
        await message.reply_text(
            f"Client for account ID `{account_id}` is already running!", quote=True
        )
        return

    status_msg = await message.reply_text(
        f"Starting client for account ID `{account_id}`...", quote=True
    )

    session = await get_session(account_id)
    session_string = session.get("session_string")
    name = session.get("name", "Unknown")

    success, _, error = await start_client(session_string, account_id)

    if success:
        await status_msg.edit_text(
            f"✅ Successfully started client for `{name}` (ID: `{account_id}`)!"
        )
    else:
        await status_msg.edit_text(
            f"❌ Failed to start client for `{name}` (ID: `{account_id}`)!\n\nError: `{error}`"
        )


@Client.on_message(filters.command(["stopclient"]) & filters.user(ADMINS))
async def stop_specific_client(client: Client, message: Message):
    if len(message.command) < 2:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/stopclient account_id`\n"
            "**Example:** `/stopclient *********`",
            quote=True,
        )
        return

    account_id = message.command[1]

    if account_id not in active_clients:
        await message.reply_text(
            f"No active client found for account ID `{account_id}`!", quote=True
        )
        return

    status_msg = await message.reply_text(
        f"Stopping client for account ID `{account_id}`...", quote=True
    )

    if await stop_client(account_id):
        await status_msg.edit_text(
            f"✅ Successfully stopped client for account ID `{account_id}`!"
        )
    else:
        await status_msg.edit_text(
            f"❌ Failed to stop client for account ID `{account_id}`!"
        )


@Client.on_message(filters.command(["activeclients"]) & filters.user(ADMINS))
async def list_active_clients(client: Client, message: Message):
    if not active_clients:
        await message.reply_text("No active clients running!", quote=True)
        return

    result = "**🔄 Active Clients:**\n\n"

    for i, (account_id, client) in enumerate(active_clients.items(), 1):
        try:
            me = await client.get_me()
            name = f"{me.first_name} {me.last_name if me.last_name else ''}"
            username = f"@{me.username}" if me.username else "No username"
            result += f"**{i}.** **ID:** `{account_id}`\n"
            result += f"   **Name:** `{name}`\n"
            result += f"   **Username:** `{username}`\n\n"
        except Exception as e:
            result += f"**{i}.** **ID:** `{account_id}`\n"
            result += f"   **Error getting info:** `{str(e)[:50]}...`\n\n"

    result += f"**Total active clients:** `{len(active_clients)}`"

    await message.reply_text(result, quote=True)


async def start_clients_on_startup():
    LOGGER.info("Starting initial clients...")

    sessions = await get_all_sessions()
    sessions = [s for s in sessions if s.get("details_populated", False)]

    if not sessions:
        LOGGER.info("No populated sessions found in the database!")
        return

    LOGGER.info(f"Found {len(sessions)} sessions. Starting clients...")

    success_count = 0
    failed_count = 0

    for session in sessions:
        account_id = session.get("account_id")
        session_string = session.get("session_string")
        name = session.get("name", "Unknown")

        if account_id in active_clients:
            success_count += 1
            continue

        success, _, error = await start_client(session_string, account_id)

        if success:
            success_count += 1
            LOGGER.info(f"Started client for {name} ({account_id})")
        else:
            failed_count += 1
            LOGGER.error(f"Failed to start client for {name} ({account_id}): {error}")

    LOGGER.info(
        f"Client startup complete. Success: {success_count}, Failed: {failed_count}, Total active: {len(active_clients)}"
    )


@Client.on_message(filters.command(["react"]))
async def react_message_command(client: Client, message: Message):
    if not await check_command_auth(client, message, "react"):
        return
    user_id = message.from_user.id
    if len(message.command) < 2 or len(message.command) > 4:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/react message_link emojis count`\n"
            "**Example:** `/react https://t.me/c/**********/12345 👍❤️ 10` or `/react t.me/channelname/67890 👍❤️ 10`\n"
            "**Note:** `count` is optional, if not provided, all clients will react to the message"
        )
        return

    try:
        link = message.command[1]
        reaction = message.command[2]
        rect = []
        for i in reaction:
            rect.append(i)
        try:
            count = message.command[3]
        except IndexError:
            count = None
    except IndexError:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/react message_link reaction count`\n"
            "**Example:** `/react https://t.me/c/**********/123 👍 10` or `/react t.me/channelname/123 👍 10`\n"
            "**Note:** `count` is optional, if not provided, all clients will react to the message"
        )
        return

    try:
        if "/c/" in link:
            # Private channel link format: t.me/c/channelid/messageid
            chat_id, message_id = link.split("/")[-2:]
            chat_id = "-100" + chat_id
            chat_id = int(chat_id)
        else:
            # Public channel link format: t.me/channelname/messageid
            try:
                chat = await user_instance.get_chat(link.split("/")[-2])
                chat_id = chat.id
            except Exception as e:
                await message.reply_text(f"**Error getting channel:** {str(e)}", quote=True)
                return
        message_id = int(link.split("/")[-1])
    except Exception as e:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/react message_link reaction count`\n"
            "**Example:** `/react https://t.me/c/**********/123 👍 10` or `/react t.me/channelname/123 👍 10`\n"
            "**Note:** `count` is optional, if not provided, all clients will react to the message"
        )
        return
    try:
        chat = await user_instance.get_chat(chat_id)
        available_reactions = chat.available_reactions
        for reaction in rect:
            if available_reactions.all_are_enabled:
                if reaction not in DEF_REC:
                    rec = ""
                    for react in DEF_REC:
                        rec += f"{react}, "
                    await message.reply_text(
                        f"**Error: Reaction {reaction} not available in chat {chat_id}**\n"
                        f"Available reactions: {rec}",
                        quote=True,
                    )
                    return
            else:
                avl_rec = ""
                for reactions in available_reactions.reactions:
                    avl_rec += f"{reactions.emoji}, "
                if reaction not in avl_rec:
                    await message.reply_text(
                        f"**Error: Reaction {reaction} not available in chat {chat_id}**\n"
                        f"Available reactions: {avl_rec}",
                        quote=True,
                    )
                    return
        re_msg = await user_instance.get_messages(chat_id, message_id)
    except Exception as e:
        await message.reply_text(f"**Error getting message:** {str(e)}", quote=True)
        LOGGER.error(
            "Error getting message: %s, chat_id: %s, message_id: %s",
            str(e),
            chat_id,
            message_id,
        )
        return
    success, failed = await send_reaction(message=re_msg, reactions=rect, count=count)
    await message.reply_text(
        f"**Reaction sent with {rect} to {success} clients successfully and {failed} failed**",
        quote=True,
    )


async def send_reaction(
    message: Message, reactions: list = None, count: int = None
) -> tuple[int, int]:
    success_count = 0
    failed_count = 0

    rec_count = 0
    for account_id in list(active_clients.keys()):
        if await react_message(account_id, message, reactions[rec_count]):
            success_count += 1
            rec_count += 1
            if rec_count >= len(reactions):
                rec_count = 0
        else:
            failed_count += 1
        if count:
            if int(success_count) >= int(count):
                break
    return success_count, failed_count


async def react_message(account_id: str, message: Message, reaction) -> bool:
    try:
        if account_id in active_clients:
            client = active_clients[account_id]
            await client.set_reaction(
                chat_id=message.chat.id,
                message_id=message.id,
                reaction=[ReactionTypeEmoji(emoji=reaction)],
            )
            LOGGER.info(f"Reacted to message {message.id} for account {account_id}")
            return True
        else:
            LOGGER.warning(
                f"Error reacting to message {message.id} for account {account_id}: No active client found"
            )
            return False
    except Exception as e:
        LOGGER.error(
            f"Error reacting to message {message.id} for account {account_id}: {str(e)}"
        )
        LOGGER.error(traceback.format_exc())
        return False


@Client.on_message(filters.command(["view"]))
async def view_message_command(client: Client, message: Message):
    user_id = message.from_user.id
    if (user_id not in await get_authorized_users()) and (user_id not in ADMINS):
        return
    if len(message.command) < 2 or len(message.command) > 3:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/view message_link count`\n"
            "**Example:** `/view https://t.me/c/**********/12345 10` or `/view t.me/channelname/67890 10`\n"
            "**Note:** `count` is optional, if not provided, all clients will view the message"
        )
        return

    try:
        link = message.command[1]
        if "/c/" in link:
            # Private channel link format: t.me/c/channelid/messageid
            chat_id, message_id = link.split("/")[-2:]
            chat_id = "-100" + chat_id
            chat_id = int(chat_id)
        else:
            # Public channel link format: t.me/channelname/messageid
            try:
                chat = await user_instance.get_chat(link.split("/")[-2])
                chat_id = chat.id
            except Exception as e:
                await message.reply_text(f"**Error getting channel:** {str(e)}", quote=True)
                return
        message_id = int(link.split("/")[-1])
        try:
            count = message.command[2]
        except IndexError:
            count = None
    except Exception:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/view message_link count`\n"
            "**Example:** `/view https://t.me/c/**********/7890 10` or `/view t.me/channelname/7890 10`\n"
            "**Note:** `count` is optional, if not provided, all clients will view the message"
        )
        return

    try:
        vw_msg = await user_instance.get_messages(chat_id, message_id)
    except Exception as e:
        await message.reply_text(f"**Error getting message:** {str(e)}", quote=True)
        return
    views = vw_msg.views if vw_msg.views else 0
    await message.reply_text(
        f"**Message view count: {views}**\n Sending views.", quote=True
    )
    success, failed = await send_views(vw_msg, count)
    await message.reply_text(
        f"**Views sent to {success} clients successfully and {failed} failed**",
        quote=True,
    )


async def send_views(message: Message, count: int = None) -> tuple[int, int]:
    success_count = 0
    failed_count = 0

    for account_id in list(active_clients.keys()):
        if await view_message(account_id, message):
            success_count += 1
        else:
            failed_count += 1
        if count:
            if int(success_count) >= int(count):
                break
    return success_count, failed_count


async def view_message(account_id: str, message: Message) -> bool:
    try:
        if account_id in active_clients:
            client = active_clients[account_id]
            await client.view_messages(message.chat.id, message.id, force_read=True)
            LOGGER.info(f"Viewed message {message.id} for account {account_id}")
            return True
        else:
            LOGGER.warning(
                f"Error viewing message {message.id} for account {account_id}: No active client found"
            )
            return False
    except Exception as e:
        LOGGER.error(
            f"Error viewing message {message.id} for account {account_id}: {str(e)}"
        )
        LOGGER.error(traceback.format_exc())
        return False


@Client.on_message(filters.command(["joinchannel"]))
async def join_channel_command(client: Client, message: Message):
    user_id = message.from_user.id
    if (user_id not in await get_authorized_users()) and (user_id not in ADMINS):
        return
    if len(message.command) < 2:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/joinchannel channel_link`\n"
            "**Example:** `/joinchannel https://t.me/+UYUIRhQ32bDIU` or `/joinchannel t.me/channelname`"
        )
        return

    channel_link = message.command[1]
    
    if "t.me/" in channel_link and not (channel_link.startswith("https://") or channel_link.startswith("http://")):
        if not channel_link.startswith("t.me/"):
            channel_link = "t.me/" + channel_link.split("t.me/")[1]
        channel_link = "https://" + channel_link
    
    try:
        await user_instance.join_chat(channel_link)
    except UserAlreadyParticipant:
        pass
    except Exception as e:
        LOGGER.error(f"Error joining channel {channel_link}: {str(e)}")
        LOGGER.error(traceback.format_exc())
        await message.reply_text(
            f"**Error joining channel {channel_link}: {str(e)}**", quote=True
        )
        return

    success, failed, channel_id, channel_name = await join_channel_process(channel_link)
    await message.reply_text(
        f"**Joined channel {channel_link} with {success} clients successfully and {failed} failed**",
        quote=True,
    )
    LOGGER.info("Joined channel %s with %s clients successfully and %s failed: User ID: %s", channel_link, success, failed, user_id)
    if success:
        await add_channel(channel_id, channel_name, message.from_user.id)
    else:
        LOGGER.error("Failed to join channel %s: User ID: %s", channel_link, user_id)


async def join_channel_process(link: str) -> tuple[int, int, int, str]:
    success_count = 0
    failed_count = 0
    channel_id = None
    channel_name = None
    for account_id in list(active_clients.keys()):
        join, c_id, c_name = await channel_join(account_id, link)
        if join:
            success_count += 1
            channel_id = c_id
            channel_name = c_name
        else:
            failed_count += 1
    return success_count, failed_count, channel_id, channel_name


async def channel_join(account_id: str, link: str) -> bool:
    try:
        if account_id in active_clients:
            client = active_clients[account_id]
            join = await client.join_chat(link)
            LOGGER.info(f"Joined channel {link} for account {account_id}")
            return True, join.id, join.title
        else:
            LOGGER.warning(
                f"Error joining channel {link} for account {account_id}: No active client found"
            )
            return False, None, None
    except (UserAlreadyParticipant, ChannelPrivate):
        return False, None, None
    except Exception as e:
        LOGGER.error(f"Error joining channel {link} for account {account_id}: {str(e)}")
        LOGGER.error(traceback.format_exc())
        return False, None, None


@Client.on_message(filters.command(["leavechannel"]))
async def leave_channel_command(client: Client, message: Message):
    user_id = message.from_user.id
    if (user_id not in await get_authorized_users()) and (user_id not in ADMINS):
        return
    if len(message.command) < 2:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/leavechannel channel_id`\n"
            "**Example:** `/leavechannel -100**********`"
        )
        return

    channel_id = message.command[1]
    try:
        await user_instance.leave_chat(channel_id)
    except (UserNotParticipant, PeerIdInvalid):
        pass
    except Exception as e:
        LOGGER.error(f"Error leaving channel {channel_id}: {str(e)}")
        LOGGER.error(traceback.format_exc())
        await message.reply_text(
            f"**Error leaving channel {channel_id}: {str(e)}**", quote=True
        )
        return
    success, failed = await leave_channel_process(channel_id)
    await message.reply_text(
        f"**Left channel {channel_id} with {success} clients successfully and {failed} failed**",
        quote=True,
    )
    LOGGER.info("Left channel %s with %s clients successfully and %s failed: User ID: %s", channel_id, success, failed, user_id)
    if success:
        await remove_channel(channel_id)
        LOGGER.info("Removed channel %s from database: User ID: %s", channel_id, user_id)
    else:
        LOGGER.error("Failed to leave channel %s: User ID: %s", channel_id, user_id)


async def leave_channel_process(channel_id: int) -> tuple[int, int]:
    success_count = 0
    failed_count = 0
    for account_id in list(active_clients.keys()):
        if await leave_channel(account_id, channel_id):
            success_count += 1
        else:
            failed_count += 1
    return success_count, failed_count


async def leave_channel(account_id: str, channel_id: int) -> bool:
    try:
        if account_id in active_clients:
            client = active_clients[account_id]
            await client.leave_chat(channel_id)
            LOGGER.info(f"Left channel {channel_id} for account {account_id}")
            return True
        else:
            LOGGER.warning(
                f"Error leaving channel {channel_id} for account {account_id}: No active client found"
            )
            return False
    except (UserNotParticipant, ChannelPrivate, PeerIdInvalid):
        return True
    except Exception as e:
        LOGGER.error(
            f"Error leaving channel {channel_id} for account {account_id}: {str(e)}"
        )
        LOGGER.error(traceback.format_exc())
        return False


@user_instance.on_message(filters.channel)
async def on_message(client: Client, message: Message):
    chat_id = message.chat.id
    if chat_id not in await get_all_channel_ids():
        return

    if message.forward_from_chat:
        try:
            posted_chat_id = message.chat.id
            posted_message_id = message.id
            posted_message = await user_instance.get_messages(posted_chat_id, posted_message_id)
            message = posted_message
        except Exception as e:
            LOGGER.error(f"Error getting posted message: {str(e)}")
            LOGGER.error(traceback.format_exc())
            return

    try:
        reactions = await get_upcoming_reactions(chat_id)
        if reactions["reactions"]:
            count = reactions["reac_count"]
            if count > 0:
                count = count
            else:
                count = None
            await send_reaction(message=message, reactions=reactions["reactions"], count=count)
    except Exception as e:
        LOGGER.error(f"Error sending views or reactions: {str(e)}")
        LOGGER.error(traceback.format_exc())
    try:
        views = await get_upcoming_views(chat_id)
        if views:
            count = views["view_count"]
            if count > 0:
                count = count
            else:
                count = None
            await send_views(message, count)
    except Exception as e:
        LOGGER.error(f"Error sending views or reactions: {str(e)}")
        LOGGER.error(traceback.format_exc())


@Client.on_message(filters.command(["msg"]))
async def bulk_send_to_all(client: Client, message: Message):
    if not await check_command_auth(client, message, "msg"):
        return
    user_id = message.from_user.id
    if len(message.command) > 1:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/msg`\n"
            "**Example:** `/msg`\n\n"
            "After using the command:\n"
            "1. Enter messages separated by '|'\n"
            "2. Enter the target username\n\n"
            "**Example input:**\n"
            "`Hello | How are you? | Nice to meet you`",
            quote=True
        )
        return
    
    fb_mss = await message.reply_text(
        "📝 Enter the messages (separate them with '|')",        
    )
       
    try:
        fb_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await fb_mss.reply_text("Request timed out, please try again.", quote=True)
        return

    fb_mssg = list(fb_cb.text.split("|"))
    await fb_mss.delete()
    
    unm_mss = await message.reply_text(
        "Now send the username of the user you want to send the message to.",        
    )
    try:
        unm_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await unm_mss.reply_text("Request timed out, please try again.", quote=True)
        return
    
    unm_mssg = unm_cb.text
    
    delay_mss = await message.reply_text(
        "📝 Enter the delay between messages in seconds:",        
    )
       
    try:
        delay_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await delay_mss.reply_text("Request timed out, please try again.", quote=True)
        return

    delay = int(delay_cb.text)
    
    status_msg = await message.reply_text(
        f"Starting to send messages to {unm_mssg} using all available clients...", 
        quote=True
    )
    
    success_count = 0
    failed_count = 0
    
    msscnt = 0
    for account_id in list(active_clients.keys()):
        if msscnt >= len(fb_mssg):
            break

        try:
            client = active_clients[account_id]
            await client.send_message(unm_mssg, fb_mssg[msscnt])
            success_count += 1
            msscnt += 1
            await asyncio.sleep(delay)
        except Exception as e:
            failed_count += 1
            LOGGER.error(f"Failed to send message using client {account_id} to {unm_mssg}. Error: {str(e)}")
            LOGGER.error(traceback.format_exc())
    
    await status_msg.edit_text(
        f"**Bulk Send Results:**\n\n"
        f"✅ Successfully sent: {success_count} messages\n"
        f"❌ Failed to send: {failed_count} messages\n"
        f"📊 Used {len(active_clients)} clients in total"
    )


@Client.on_message(filters.command(["photo"]))
async def bulk_send_photo_to_all(client: Client, message: Message):
    if not await check_command_auth(client, message, "photo"):
        return
    user_id = message.from_user.id
    if len(message.command) > 1:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/photo`\n"
            "**Example:** `/photo`\n\n"
            "After using the command:\n"
            "1. Enter photo messages in format: `caption, image_url`\n"
            "2. Separate multiple photos with '|'\n"
            "3. Enter the target username\n\n"
            "**Example input:**\n"
            "`Hello, https://example.com/image1.jpg | World, https://example.com/image2.jpg`",
            quote=True
        )
        return
    
    photo_mss = await message.reply_text(
        "📝 Enter the messages and image links in format: text1, imagelink1 | text2, imagelink2 | text3, imagelink3",        
    )
       
    try:
        photo_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await photo_mss.reply_text("Request timed out, please try again.", quote=True)
        return

    photo_data = []
    photo_entries = photo_cb.text.split("|")
    
    for entry in photo_entries:
        parts = entry.strip().split(",", 1)
        if len(parts) == 2:
            caption = parts[0].strip()
            image_url = parts[1].strip()
            photo_data.append({"caption": caption, "image_url": image_url})
    
    if not photo_data:
        await photo_mss.reply_text("Invalid format. Please try again with the correct format.", quote=True)
        return
        
    await photo_mss.delete()
    
    unm_mss = await message.reply_text(
        "Now send the username of the user you want to send the photos to.",        
    )
    try:
        unm_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await unm_mss.reply_text("Request timed out, please try again.", quote=True)
        return
    
    unm_mssg = unm_cb.text
    
    delay_mss = await message.reply_text(
        "📝 Enter the delay between photos in seconds:",        
    )
       
    try:
        delay_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await delay_mss.reply_text("Request timed out, please try again.", quote=True)
        return

    delay = int(delay_cb.text)
    
    status_msg = await message.reply_text(
        f"Starting to send photos to {unm_mssg} using all available clients...", 
        quote=True
    )
    
    success_count = 0
    failed_count = 0
    
    photo_idx = 0
    for account_id in list(active_clients.keys()):
        if photo_idx >= len(photo_data):
            break

        try:
            client = active_clients[account_id]
            current_photo = photo_data[photo_idx]
            await client.send_photo(
                unm_mssg, 
                current_photo["image_url"], 
                caption=current_photo["caption"]
            )
            success_count += 1
            photo_idx += 1
            await asyncio.sleep(delay)
        except Exception as e:
            failed_count += 1
            LOGGER.error(f"Failed to send photo using client {account_id} to {unm_mssg}. Error: {str(e)}")
            LOGGER.error(traceback.format_exc())
    
    await status_msg.edit_text(
        f"**Bulk Photo Send Results:**\n\n"
        f"✅ Successfully sent: {success_count} photos\n"
        f"❌ Failed to send: {failed_count} photos\n"
        f"📊 Used {len(active_clients)} clients in total"
    )


@Client.on_message(filters.command(["video"]))
async def bulk_send_video_to_all(client: Client, message: Message):
    if not await check_command_auth(client, message, "video"):
        return
    user_id = message.from_user.id
    if len(message.command) > 1:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/video`\n"
            "**Example:** `/video`\n\n"
            "After using the command:\n"
            "1. Enter video messages in format: `caption, video_url`\n"
            "2. Separate multiple videos with '|'\n"
            "3. Enter the target username\n\n"
            "**Example input:**\n"
            "`My video 1, https://example.com/video1.mp4 | My video 2, https://example.com/video2.mp4`",
            quote=True
        )
        return
    
    video_mss = await message.reply_text(
        "📝 Enter the messages and video links in format: text1, videolink1 | text2, videolink2 | text3, videolink3",        
    )
       
    try:
        video_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await video_mss.reply_text("Request timed out, please try again.", quote=True)
        return

    video_data = []
    video_entries = video_cb.text.split("|")
    
   
    for entry in video_entries:
        parts = entry.strip().split(",", 1)
        if len(parts) == 2:
            caption = parts[0].strip()
            video_url = parts[1].strip()
            video_data.append({"caption": caption, "video_url": video_url})
    
    if not video_data:
        await video_mss.reply_text("Invalid format. Please try again with the correct format.", quote=True)
        return
        
    await video_mss.delete()
    
    unm_mss = await message.reply_text(
        "Now send the username of the user you want to send the videos to.",        
    )
    try:
        unm_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await unm_mss.reply_text("Request timed out, please try again.", quote=True)
        return
    
    unm_mssg = unm_cb.text
    
    delay_mss = await message.reply_text(
        "📝 Enter the delay between videos in seconds:",        
    )
       
    try:
        delay_cb = await client.listen_message(message.chat.id, filters.text, timeout=300)
    except TimeoutError:
        await delay_mss.reply_text("Request timed out, please try again.", quote=True)
        return

    delay = int(delay_cb.text)

    status_msg = await message.reply_text(
        f"Starting to send videos to {unm_mssg} using all available clients...", 
        quote=True
    )
    
    success_count = 0
    failed_count = 0
    
    video_idx = 0
    for account_id in list(active_clients.keys()):
        if video_idx >= len(video_data):
            break

        try:
            client = active_clients[account_id]
            current_video = video_data[video_idx]
            await client.send_video(
                unm_mssg, 
                current_video["video_url"], 
                caption=current_video["caption"]
            )
            success_count += 1
            video_idx += 1
            await asyncio.sleep(delay)
        except Exception as e:
            failed_count += 1
            LOGGER.error(f"Failed to send video using client {account_id} to {unm_mssg}. Error: {str(e)}")
            LOGGER.error(traceback.format_exc())
    
    await status_msg.edit_text(
        f"**Bulk Video Send Results:**\n\n"
        f"✅ Successfully sent: {success_count} videos\n"
        f"❌ Failed to send: {failed_count} videos\n"
        f"📊 Used {len(active_clients)} clients in total"
    )


@Client.on_message(filters.command(["userstatus"]) & filters.user(ADMINS))
async def list_user_status(client: Client, message: Message):
    users = await get_all_users()
    
    if not users:
        await message.reply_text("No users found in the database.", quote=True)
        return
    
    text_file = "user_status.txt"
    with open(text_file, "w", encoding="utf-8") as f:
        f.write("🔰 User Status Report 🔰\n\n")
        
        for user in users:
            user_id = user.get("user_id", "Unknown")
            username = user.get("username", "No username")
            first_name = user.get("first_name", "No name")
            authorized = "✅" if user.get("authorized", False) else "❌"
            mod = "✅" if user.get("mod", False) else "❌"
            session_mod = "✅" if user.get("session_mod", False) else "❌"
            
            commands = await get_user_commands(user_id)
            approved_commands = [cmd for cmd, status in commands.items() if status]
            
            # Write user info to file
            f.write(f"👤 User: {first_name} (@{username})\n")
            f.write(f"🆔 ID: {user_id}\n")
            f.write(f"🔑 Authorized: {authorized}\n")
            f.write(f"👮 Mod: {mod}\n")
            f.write(f"🔧 Session Mod: {session_mod}\n")
            f.write("✅ Approved Commands:\n")
            if approved_commands:
                for cmd in approved_commands:
                    f.write(f"  • /{cmd}\n")
            else:
                f.write("  • None\n")
            f.write("\n" + "="*50 + "\n\n")
    
    summary = "🔰 User Status Summary 🔰\n\n"
    summary += f"Total Users: {len(users)}\n"
    summary += f"Authorized Users: {len([u for u in users if u.get('authorized', False)])}\n"
    summary += f"Moderators: {len([u for u in users if u.get('mod', False)])}\n"
    summary += f"Session Moderators: {len([u for u in users if u.get('session_mod', False)])}\n\n"
    summary += "📎 Full report has been sent as a file."
    
    await message.reply_text(summary, quote=True)
    
    try:
        await message.reply_document(
            text_file,
            caption="Detailed user status report"
        )
    except Exception as e:
        await message.reply_text(f"Error sending file: {str(e)}", quote=True)
    
    try:
        os.remove(text_file)
    except Exception:
        pass


@Client.on_message(filters.command(["removeperm"]) & filters.user(ADMINS))
async def remove_permissions(client: Client, message: Message):
    """Remove permissions from a user"""
    if len(message.command) < 3:
        await message.reply_text(
            "**Invalid command format!**\n\n"
            "**Format:** `/removeperm user_id permission_type`\n"
            "**Example:** `/removeperm ********* react`\n\n"
            "**Available permission types:**\n"
            "• `all` - Remove all command permissions\n"
            "• `mod` - Remove moderator status\n"
            "• `session_mod` - Remove session moderator status\n"
            "• `command_name` - Remove specific command permission\n"
            "  Available commands: listchannels, joinchannel, leavechannel, addupview, rmupview, addupreaction, rmupreaction, react, view, msg, photo, video",
            quote=True
        )
        return

    try:
        user_id = int(message.command[1])
        perm_type = message.command[2].lower()
    except ValueError:
        await message.reply_text("Invalid user ID! Please provide a valid numeric ID.", quote=True)
        return

    # Get user's current permissions
    commands = await get_user_commands(user_id)
    if not commands:
        await message.reply_text("User not found in database!", quote=True)
        return

    status_msg = await message.reply_text("Processing permission removal...", quote=True)

    if perm_type == "all":
        # Remove all command permissions
        success_count = 0
        for cmd in commands.keys():
            if await revoke_command(user_id, cmd):
                success_count += 1
        
        await status_msg.edit_text(
            f"✅ Removed all command permissions from user {user_id}\n"
            f"Successfully revoked {success_count} command permissions."
        )

    elif perm_type == "mod":
        # Remove moderator status
        if await remove_mod(user_id):
            await status_msg.edit_text(f"✅ Removed moderator status from user {user_id}")
        else:
            await status_msg.edit_text(f"❌ Failed to remove moderator status from user {user_id}")

    elif perm_type == "session_mod":
        # Remove session moderator status
        if await remove_session_mod(user_id):
            await status_msg.edit_text(f"✅ Removed session moderator status from user {user_id}")
        else:
            await status_msg.edit_text(f"❌ Failed to remove session moderator status from user {user_id}")

    elif perm_type in commands:
        # Remove specific command permission
        if await revoke_command(user_id, perm_type):
            await status_msg.edit_text(f"✅ Removed permission for command /{perm_type} from user {user_id}")
        else:
            await status_msg.edit_text(f"❌ Failed to remove permission for command /{perm_type} from user {user_id}")

    else:
        await status_msg.edit_text(
            "❌ Invalid permission type!\n\n"
            "**Available permission types:**\n"
            "• `all` - Remove all command permissions\n"
            "• `mod` - Remove moderator status\n"
            "• `session_mod` - Remove session moderator status\n"
            "• `command_name` - Remove specific command permission\n"
            "  Available commands: listchannels, joinchannel, leavechannel, addupview, rmupview, addupreaction, rmupreaction, react, view, msg, photo, video"
        )


try:
    loop = asyncio.get_event_loop()
    if loop.is_running():
        loop.create_task(start_clients_on_startup())
    else:
        loop.run_until_complete(start_clients_on_startup())
except RuntimeError:
    asyncio.run(start_clients_on_startup())
