import re
from datetime import datetime, timedelta
from pyrogram import Client, filters
from pyrogram.types import (
    Message,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    CallbackQuery,
)
from vmanager import ADMINS, LOGGER
from vmanager.db import (
    add_user,
    add_auth_user,
    is_user_authorized,
    authorize_user,
    remove_auth_user,
    get_all_users,
    get_authorized_users,
    user_on,
    user_off,
    add_mod,
    remove_mod,
    add_session_mod,
    remove_session_mod,
    add_upcoming_view,
    remove_upcoming_view,
    add_upcoming_reaction,
    remove_upcoming_reaction,
    get_all_channels,
    get_mod_users,
)
from vmanager.plugins.command_auth import check_command_auth


@Client.on_message(filters.command("start") & filters.private)
async def start_command(client: Client, message: Message):
    user_id = message.from_user.id
    name = message.from_user.first_name if message.from_user.first_name else " "
    user_name = "@" + message.from_user.username if message.from_user.username else None
    await add_user(user_id, user_name)

    if await is_user_authorized(user_id):
        request_button = None
        msg = f"Hello {name}!\n\n You already have access."
    else:
        await add_auth_user(user_id, user_name, name)
        msg = f"Hello {name}!\nYou are not authorized please contact Admin"
        request_button = InlineKeyboardMarkup(
            [
                [
                    InlineKeyboardButton(
                        "✅ Get Approval", callback_data="request_access"
                    ),
                ]
            ]
        )

    await message.reply_text(msg, reply_markup=request_button)


@Client.on_callback_query(filters.regex(r"^request_access$"))
async def request_access_callback(client: Client, callback_query: CallbackQuery):
    user_id = callback_query.from_user.id
    name = (
        callback_query.from_user.first_name
        if callback_query.from_user.first_name
        else " "
    )
    user_name = (
        "@" + callback_query.from_user.username
        if callback_query.from_user.username
        else None
    )

    await callback_query.edit_message_text(
        "Thank you for your interest! Your request has been sent to the admin for approval. "
        "You will be notified once your request is processed."
    )

    user_mention = user_name if user_name else name
    admin_notification = f"New user request from {user_mention} (ID: `{user_id}`)"

    approval_keyboard = InlineKeyboardMarkup(
        [
            [
                InlineKeyboardButton("Approve ✅", callback_data=f"approve_{user_id}"),
                InlineKeyboardButton("Reject ❌", callback_data=f"reject_{user_id}"),
            ]
        ]
    )

    for admin in ADMINS:
        try:
            await client.send_message(
                chat_id=admin,
                text=admin_notification,
                reply_markup=approval_keyboard,
            )
        except Exception:
            LOGGER.error(
                f"Failed to send approval request to admin {admin}. User ID: {user_id}"
            )


@Client.on_callback_query(
    filters.user(ADMINS) & filters.regex(r"^(approve|reject)_(\d+)$")
)
async def handle_approval_callback(client: Client, callback_query: CallbackQuery):
    action, user_id = callback_query.data.split("_")
    user_id = int(user_id)

    if action == "approve":
        success = await authorize_user(user_id)
        if success:
            await callback_query.edit_message_text(f"✅ User {user_id} has been approved successfully.")
            try:
                await client.send_message(
                    chat_id=user_id,
                    text="✅ Your request has been approved!",
                )
            except Exception as e:
                LOGGER.error(
                    f"Failed to send approval message to user {user_id}. Error: {str(e)}"
                )
                for admin in ADMINS:
                    try:
                        await client.send_message(
                            chat_id=admin,
                            text=f"Failed to notify user {user_id}. Error: {str(e)}",
                        )
                    except Exception as e:
                        LOGGER.error(
                            f"Failed to send message to admin {admin}. Error: {str(e)}"
                        )
        else:
            await callback_query.edit_message_text(f"❌ Failed to approve user {user_id}. Please try again.")

    elif action == "reject":
        await callback_query.edit_message_text(f"User {user_id} has been rejected.")
        try:
            await client.send_message(
                chat_id=user_id, text="❌ Your request has been declined."
            )
        except Exception as e:
            LOGGER.error(
                f"Failed to send rejection message to user {user_id}. Error: {str(e)}"
            )
            for admin in ADMINS:
                try:
                    await client.send_message(
                        chat_id=admin,
                        text=f"Failed to notify user {user_id}. Error: {str(e)}",
                    )
                except Exception as e:
                    LOGGER.error(
                        f"Failed to send message to admin {admin}. Error: {str(e)}"
                    )


@Client.on_message(filters.command("users") & filters.user(ADMINS))
async def list_users(client: Client, message: Message):
    users = await get_all_users()

    if not users:
        await message.reply_text("No users in database.")
        return

    user_list = []
    for userr in users:
        authorized_status = (
            "✅ Authorized" if userr.get("authorized", False) else "⏳ Pending"
        )
        username = userr.get("username", "No username")
        user_id = userr.get("user_id", "Unknown")
        user_list.append(f"• ID: `{user_id}` | @{username} | {authorized_status}")

    full_text = "Registered Users:\n\n" + "\n".join(user_list)

    if len(full_text) <= 4096:
        await message.reply_text(full_text)
    else:
        file_name = "registered_users.txt"
        with open(file_name, "w", encoding="utf-8") as f:
            f.write(full_text)

        await message.reply_document(file_name)


@Client.on_message(filters.command("remove") & filters.user(ADMINS))
async def remove_user_command(client: Client, message: Message):
    command_parts = message.text.split()

    if len(command_parts) != 2:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/remove USER_ID`\n\n"
            "Example: `/remove 123456789`"
        )
        return

    try:
        user_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ User ID must be a number.")
        return

    success = await remove_auth_user(user_id)

    if success:
        await message.reply_text(f"✅ User {user_id} has been removed from the system.")
        try:
            await client.send_message(
                chat_id=user_id,
                text="You've been removed from the channel subscription service.",
            )
        except Exception:
            pass
    else:
        await message.reply_text(f"❌ User {user_id} not found in the system.")


@Client.on_callback_query(filters.regex(r"^start_messages$"))
async def start_messages_callback(client: Client, callback_query: CallbackQuery):
    user_id = callback_query.from_user.id

    if not await is_user_authorized(user_id):
        await callback_query.answer(
            "You are not authorized to use this bot.", show_alert=True
        )
        return

    await user_on(user_id)

    stop_button = InlineKeyboardMarkup(
        [[InlineKeyboardButton("Stop", callback_data="stop_messages")]]
    )

    await callback_query.edit_message_text(
        "✅ You will now receive channel messages again. Click the button below to stop.",
        reply_markup=stop_button,
    )


@Client.on_callback_query(filters.regex(r"^stop_messages$"))
async def stop_messages_callback(client: Client, callback_query: CallbackQuery):
    user_id = callback_query.from_user.id

    await user_off(user_id)

    start_button = InlineKeyboardMarkup(
        [[InlineKeyboardButton("Start Again", callback_data="start_messages")]]
    )

    await callback_query.edit_message_text(
        "✅ You will no longer receive channel messages. Click the button below to restart.",
        reply_markup=start_button,
    )


@Client.on_message(filters.command(["addmod"]) & filters.user(ADMINS))
async def add_mod_command(client: Client, message: Message):
    command_parts = message.text.split()

    if len(command_parts) != 2:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/addmod USER_ID`\n\n"
            "Example: `/addmod 123456789`"
        )
        return
    try:
        user_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ User ID must be a number.")
        return
    success = await add_mod(user_id)
    if success:
        await message.reply_text(f"✅ User {user_id} has been added as an admin.")
    else:
        await message.reply_text(
            f"❌ Unable to add user {user_id} as an admin. Please check if the user is already a mod."
        )


@Client.on_message(filters.command(["addsessionmod"]) & filters.user(ADMINS))
async def add_session_mod_command(client: Client, message: Message):
    command_parts = message.text.split()

    if len(command_parts) != 2:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/addsessionmod USER_ID`\n\n"
            "Example: `/addsessionmod 123456789`"
        )
        return
    try:
        user_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ User ID must be a number.")
        return
    success = await add_session_mod(user_id)
    if success:
        await message.reply_text(
            f"✅ User {user_id} has been added as a session admin."
        )
    else:
        await message.reply_text(
            f"❌ Unable to add user {user_id} as a session admin. Please check if the user is already a session mod."
        )


@Client.on_message(filters.command(["rmmod"]) & filters.user(ADMINS))
async def remove_mod_command(client: Client, message: Message):
    command_parts = message.text.split()

    if len(command_parts) != 2:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/rmmod USER_ID`\n\n"
            "Example: `/rmmod 123456789`"
        )
        return
    try:
        user_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ User ID must be a number.")
        return
    success = await remove_mod(user_id)
    if success:
        await message.reply_text(f"✅ User {user_id} has been removed as an admin.")
    else:
        await message.reply_text(
            f"❌ Unable to remove user {user_id} as an admin. Please check if the user is a mod."
        )


@Client.on_message(filters.command(["rmsessionmod"]) & filters.user(ADMINS))
async def remove_session_mod_command(client: Client, message: Message):
    command_parts = message.text.split()

    if len(command_parts) != 2:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/rmsessionmod USER_ID`\n\n"
            "Example: `/rmsessionmod 123456789`"
        )
        return
    try:
        user_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ User ID must be a number.")
        return
    success = await remove_session_mod(user_id)
    if success:
        await message.reply_text(
            f"✅ User {user_id} has been removed as a session mod."
        )
    else:
        await message.reply_text(
            f"❌ Unable to remove user {user_id} as a session admin. Please check if the user is a session mod."
        )


@Client.on_message(filters.command(["addupview"]))
async def add_up_views_command(client: Client, message: Message):
    command_parts = message.text.split()
    user_id = message.from_user.id
    
    if not await check_command_auth(client, message, "addupview"):
        return

    if len(command_parts) != 3:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/addupview channel_id count`\n\n"
            "Example: `/addupview 123456789 10`"
        )
        return
    try:
        channel_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ Channel ID must be a number.")
        return
    try:
        count = int(command_parts[2])
    except ValueError:
        await message.reply_text("❌ Count must be a number.")
        return
    success = await add_upcoming_view(channel_id, count)
    if success:
        await message.reply_text(f"✅ Channel {channel_id} has been added in the upcoming views list.")
    else:
        await message.reply_text(
            f"❌ Unable to add channel {channel_id} in the upcoming views list. Please check if the channel is joined."
        )


@Client.on_message(filters.command(["rmupview"]))
async def remove_up_views_command(client: Client, message: Message):
    command_parts = message.text.split()
    user_id = message.from_user.id
    if (user_id not in await get_authorized_users()) and (user_id not in ADMINS):
        return

    if len(command_parts) != 2:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/rmupview channel_id`\n\n"
            "Example: `/rmupview -100123456789`"
        )
        return
    try:
        channel_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ Channel ID must be a number.")
        return
    success = await remove_upcoming_view(channel_id)
    if success:
        await message.reply_text(f"✅ Channel {channel_id} has been removed from the upcoming views list.")
    else:
        await message.reply_text(
            f"❌ Unable to remove channel {channel_id} from the upcoming views list. Please check if the channel is joined."
        )


@Client.on_message(filters.command(["addupreaction"]))
async def add_up_reactions_command(client: Client, message: Message):
    command_parts = message.text.split()
    user_id = message.from_user.id
    if not await check_command_auth(client, message, "addupreaction"):
        return
    if len(command_parts) != 3 and len(command_parts) != 4:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/addupreaction channel_id reaction count`\n\n"
            "Example: `/addupreaction -100123456789 🔥 10`"
        )
        return
    try:
        channel_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ Channel ID must be a number.")
        return
    reaction = command_parts[2]
    try:
        count = int(command_parts[3])
    except Exception:
        count = 0
    rec = []
    for i in reaction:
        rec.append(i)
    
    success = await add_upcoming_reaction(channel_id, rec, count)
    if success:
        await message.reply_text(f"✅ Channel {channel_id} has been added in the upcoming reactions list.")
    else:
        await message.reply_text(
            f"❌ Unable to add channel {channel_id} in the upcoming reactions list. Please check if the channel is joined."
        )


@Client.on_message(filters.command(["rmupreaction"]))
async def remove_up_reactions_command(client: Client, message: Message):
    command_parts = message.text.split()
    user_id = message.from_user.id
    if not await check_command_auth(client, message, "rmupreaction"):
        return

    if len(command_parts) != 2:
        await message.reply_text(
            "❌ Invalid command format.\n\nUsage: `/rmupreaction channel_id`\n\n"
            "Example: `/rmupreaction 123456789`"
        )
        return
    try:
        channel_id = int(command_parts[1])
    except ValueError:
        await message.reply_text("❌ Channel ID must be a number.")
        return
    success = await remove_upcoming_reaction(channel_id)
    if success:
        await message.reply_text(f"✅ Channel {channel_id} has been removed from the upcoming reactions list.")
    else:
        await message.reply_text(
            f"❌ Unable to remove channel {channel_id} from the upcoming reactions list. Please check if the channel is joined."
        )
        

@Client.on_message(filters.command(["listchannels"]))
async def list_channels_command(client: Client, message: Message):
    user_id = message.from_user.id
    if not await check_command_auth(client, message, "listchannels"):
        return
    
    all_channels = await get_all_channels()
    if not all_channels:
        await message.reply_text("No channels in database.")
        return
    
    channel_list = []
    for channel in all_channels:
        channel_id = channel.get("channel_id")
        channel_name = channel.get("channel_name")
        user_id = channel.get("user_id")
        up_views = channel.get("upcoming_views")
        up_react = channel.get("upcoming_reactions")
        channel_list.append(f"• ID: `{channel_id}` | {channel_name} | User ID: `{user_id}` | Upcoming Views: `{up_views}` | Upcoming Reactions: `{up_react}`")
    
    full_text = "Joined Channels:\n\n" + "\n".join(channel_list)
    
    if len(full_text) <= 4096:
        await message.reply_text(full_text)
    else:
        file_name = "joined_channels.txt"
        with open(file_name, "w", encoding="utf-8") as f:
            f.write(full_text)
        await message.reply_document(file_name)