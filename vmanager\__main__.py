import uvloop
import asyncio

uvloop.install()

from pyropatch import pyropatch 
from pyrogram import Client, __version__, idle  # noqa: E402
from pyrogram.raw.all import layer  # noqa: E402
from vmanager import APP_ID, API_HASH, BOT_TOKEN, SESSION_STRING, LOGGER  # noqa: E402


app = None
user = None


async def main():
    global app, user

    plugins = dict(root="vmanager/plugins")
    app = Client(
        name="vmanager",
        api_id=APP_ID,
        api_hash=API_HASH,
        bot_token=BOT_TOKEN,
        plugins=plugins,
        skip_updates=False,
        workers=500,
    )
    user = Client(
        name="user_vmanager",
        session_string=SESSION_STRING,
        api_id=APP_ID,
        api_hash=API_HASH,
        skip_updates=False,
        plugins=plugins,
        workers=500,
    )

    async with app, user:
        bot_me = await app.get_me()
        user_me = await user.get_me()

        LOGGER.info(
            "Bot: %s - @%s - Pyrogram v%s (Layer %s) - Started...",
            bot_me.first_name,
            bot_me.username,
            __version__,
            layer,
        )

        LOGGER.info(
            "User: %s - @%s - Started...",
            user_me.first_name,
            user_me.username,
        )

        await idle()

        LOGGER.info("Bot: %s - @%s - Stopped !!!", bot_me.first_name, bot_me.username)
        LOGGER.info(
            "User: %s - @%s - Stopped !!!", user_me.first_name, user_me.username
        )


if __name__ == "__main__":
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            loop.create_task(main())
        else:
            loop.run_until_complete(main())
    except RuntimeError:
        asyncio.run(main())
