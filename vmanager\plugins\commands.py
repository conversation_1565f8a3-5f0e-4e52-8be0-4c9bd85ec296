import os
import sys
import asyncio
import time
import shutil
from psutil import cpu_percent, virtual_memory, disk_usage
from pyrogram import Client, filters

# from pyrogram.types import LinkPreviewOptions
from vmanager.utils.util_support import humanbytes, get_db_size
from vmanager import LOGGER, ADMINS


@Client.on_message(filters.command(["restart"]) & filters.user(ADMINS))
async def restart(bot, update):
    LOGGER.warning("Restarting bot using /restart command")
    msg = await update.reply_text(text="__Restarting.....__", quote=True)
    await asyncio.sleep(5)
    await msg.edit("__Bot restarted !__")
    os.execv(sys.executable, ["python3", "-m", "vmanager"] + sys.argv)


@Client.on_message(filters.command(["logs"]) & filters.user(ADMINS))
async def log_file(bot, update):
    logs_msg = await update.reply("__Sending logs, please wait...__", quote=True)
    try:
        await update.reply_document("logs.txt")
    except Exception as e:
        await update.reply(str(e))
    await logs_msg.delete()


@Client.on_message(filters.command(["server"]) & filters.user(ADMINS))
async def server_stats(bot, update):
    sts = await update.reply_text("__Calculating, please wait...__", quote=True)
    total, used, free = shutil.disk_usage(".")
    ram = virtual_memory()
    start_t = time.time()
    end_t = time.time()
    time_taken_s = (end_t - start_t) * 1000

    ping = f"{time_taken_s:.3f} ms"
    total = humanbytes(total)
    used = humanbytes(used)
    free = humanbytes(free)
    t_ram = humanbytes(ram.total)
    u_ram = humanbytes(ram.used)
    f_ram = humanbytes(ram.available)
    cpu_usage = cpu_percent()
    ram_usage = virtual_memory().percent
    used_disk = disk_usage("/").percent
    db_size = get_db_size()

    stats_msg = f"--**BOT STATS**--\n`Ping: {ping}`\n\n--**SERVER DETAILS**--\n`Disk Total/Used/Free: {total}/{used}/{free}\nDisk usage: {used_disk}%\nRAM Total/Used/Free: {t_ram}/{u_ram}/{f_ram}\nRAM Usage: {ram_usage}%\nCPU Usage: {cpu_usage}%`\n\n--**DATABASE DETAILS**--\n`{db_size}`"
    try:
        await sts.edit(stats_msg)
    except Exception as e:
        await update.reply_text(str(e), quote=True)


@Client.on_message(filters.command(["help"]) & filters.user(ADMINS))
async def help_command(bot, update):
    help_text = (
        "**🔰 View Manager Bot Commands 🔰**\n\n"
        "**User Management:**\n"
        "• `/users` - List all registered users\n"
        "• `/remove USER_ID` - Remove a user from the system\n"
        "• `/addmod USER_ID` - Add a user as a moderator\n"
        "• `/rmmod USER_ID` - Remove moderator status\n"
        "• `/addsessionmod USER_ID` - Add a user as session moderator\n"
        "• `/rmsessionmod USER_ID` - Remove session moderator status\n\n"
        
        "**Channel Management:**\n"
        "• `/listchannels` - List all joined channels with their status\n"
        "• `/joinchannel LINK` - Join a channel with the user account\n"
        "• `/leavechannel CHANNEL_ID` - Leave a channel\n"
        "• `/addupview CHANNEL_ID COUNT` - Add a channel to auto-view messages\n"
        "• `/rmupview CHANNEL_ID` - Remove a channel from auto-view\n"
        "• `/addupreaction CHANNEL_ID EMOJIS COUNT` - Add a channel to auto-react to messages\n"
        "• `/rmupreaction CHANNEL_ID` - Remove a channel from auto-reactions\n\n"
        
        "**Reaction & View Commands:**\n"
        "• `/react MESSAGE_LINK EMOJIS COUNT` - React to a message\n"
        "• `/view MESSAGE_LINK COUNT` - View a message\n\n"
        
        "**Bulk Send Commands:**\n"
        "• `/msg` - Send multiple text messages\n"
        "• `/photo` - Send multiple photos with captions\n"
        "• `/video` - Send multiple videos with captions\n\n"
        
        "**Client Management:**\n"
        "• `/add SESSION_STRING` - Add a new Telegram session\n"
        "• `/delete ACCOUNT_ID` - Delete a Telegram session\n"
        "• `/listaccounts` - List all registered sessions\n"
        "• `/startall` - Start all clients\n"
        "• `/stopall` - Stop all clients\n"
        "• `/startclient ACCOUNT_ID` - Start a specific client\n"
        "• `/stopclient ACCOUNT_ID` - Stop a specific client\n"
        "• `/activeclients` - Show active clients\n\n"
        
        "**Moderation:**\n"
        "• `/ban USER_ID` - Ban a user\n"
        "• `/unban USER_ID` - Unban a user\n\n"
        
        "**System:**\n"
        "• `/restart` - Restart the bot\n"
        "• `/logs` - Get bot logs\n"
        "• `/server` - Get server & bot stats\n"
        "• `/stats` - Get bot user details\n"
        "• `/broadcast` - Broadcast a message to all users\n\n"
        
        "**Help:**\n"
        "• `/help` - Show this help message\n\n"
        
        "**Note:** All commands are now accessible by authorized users and admins"
    )
    await update.reply_text(help_text, quote=True)
