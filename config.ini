[loggers]
keys=root,Logger

[handlers]
keys=console<PERSON><PERSON><PERSON>, file_handler

[formatters]
keys=Formatter

[logger_root]
level=INFO
handlers=consoleHandler, file_handler

[logger_Logger]
level=INFO
handlers=consoleHandler, file_handler
qualname=Logger
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=Formatter
args=(sys.stdout,)

[handler_file_handler]
class=FileHandler
level=INFO
formatter=Formatter
args=('logs.txt','w',)

[formatter_Formatter]
format= [%(asctime)s][%(name)s][%(module)s][%(lineno)d][%(levelname)s] -> %(message)s
datefmt= %d/%m/%Y %H:%M:%S