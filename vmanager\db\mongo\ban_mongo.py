import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import DuplicateKeyError
from vmanager import DB_URL


client = AsyncIOMotorClient(DB_URL)
db = client["vmanager"]
banlist_collection = db["banlist"]

INSERTION_LOCK = asyncio.Lock()


async def ban_user(user_id):
    async with INSERTION_LOCK:
        try:
            await banlist_collection.insert_one({"user_id": user_id})
            return True
        except DuplicateKeyError:
            return False


async def is_banned(user_id):
    async with INSERTION_LOCK:
        result = await banlist_collection.find_one({"user_id": user_id})
        return result is not None


async def unban_user(user_id):
    async with INSERTION_LOCK:
        await banlist_collection.delete_one({"user_id": user_id})
        return True
