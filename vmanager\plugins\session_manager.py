import logging
import traceback
from pyrogram import Client, filters
from pyrogram.types import Message
from vmanager import ADMINS, APP_ID, API_HASH
from vmanager.db import (
    add_session,
    delete_session,
    get_all_sessions,
    get_session,
    update_session_details,
    is_session_exists,
    is_account_exists,
    get_session_mod_users,
)


async def create_client_and_get_info(session_string):
    try:
        temp_client = Client(
            "temp_session",
            api_id=APP_ID,
            api_hash=API_HASH,
            session_string=session_string,
            in_memory=True,
        )

        await temp_client.start()
        me = temp_client.me
        account_id = str(me.id)
        name = f"{me.first_name} {me.last_name if me.last_name else ''}"
        username = me.username if me.username else "Not available"
        await temp_client.stop()

        return True, {"account_id": account_id, "name": name, "username": username}
    except Exception as e:
        logging.error(f"Error creating client: {str(e)}")
        logging.error(traceback.format_exc())
        return False, str(e)


@Client.on_message(filters.command(["add"]))
async def add_account(client: Client, message: Message):
    user_id = message.from_user.id
    if (user_id not in await get_session_mod_users()) and (user_id not in ADMINS):
        return
    try:
        if len(message.command) < 2:
            await message.reply_text(
                "**Invalid command format!**\n\n"
                "**Format:** `/add session_string`\n"
                "**Example:** `/add 1BQANOTEuMTA3LjEu...`",
                quote=True,
            )
            return

        session_string = message.command[1]

        temp_id = await add_session(session_string)
        processing_msg = await message.reply_text(
            "Session added to database. Now fetching account details...", quote=True
        )

        success, result = await create_client_and_get_info(session_string)

        if success:
            account_id = result["account_id"]
            name = result["name"]
            username = result["username"]
            await update_session_details(temp_id, account_id, name, username)

            await processing_msg.edit_text(
                f"✅ **Successfully added session:**\n\n"
                f"**Account ID:** `{account_id}`\n"
                f"**Name:** `{name}`\n"
                f"**Username:** `@{username}`"
                if username
                else ""
            )
        else:
            await processing_msg.edit_text(
                f"❌ **Failed to get account details:**\n\n"
                f"Error: `{result}`\n\n"
                f"The session was added with a temporary ID: `{temp_id}`\n"
                f"You may try to delete it using: `/delete {temp_id}`"
            )

    except Exception as e:
        await message.reply_text(f"Error: {str(e)}", quote=True)


@Client.on_message(filters.command(["delete"]))
async def delete_account(client: Client, message: Message):
    user_id = message.from_user.id
    if (user_id not in await get_session_mod_users()) and (user_id not in ADMINS):
        return
    try:
        if len(message.command) < 2:
            await message.reply_text(
                "**Invalid command format!**\n\n"
                "**Format:** `/delete account_id`\n"
                "**Example:** `/delete *********`",
                quote=True,
            )
            return

        account_id = message.command[1]
        if not await is_account_exists(account_id):
            await message.reply_text(
                f"Session with Account ID `{account_id}` not found!", quote=True
            )
            return

        if await delete_session(account_id):
            await message.reply_text(
                f"Successfully deleted session with Account ID `{account_id}`",
                quote=True,
            )
        else:
            await message.reply_text(
                f"Failed to delete session with Account ID `{account_id}`", quote=True
            )

    except Exception as e:
        await message.reply_text(f"Error: {str(e)}", quote=True)


@Client.on_message(filters.command(["listaccounts"]))
async def list_accounts(client: Client, message: Message):
    user_id = message.from_user.id
    if (user_id not in await get_session_mod_users()) and (user_id not in ADMINS):
        return
    try:
        sessions = await get_all_sessions()

        if not sessions:
            await message.reply_text("No sessions found in the database!", quote=True)
            return

        sessions_text = "**📋 Available Sessions:**\n\n"

        populated_count = 0
        unpopulated_count = 0
        active_count = 0
        for i, session in enumerate(sessions, 1):
            account_id = session.get("account_id", "N/A")
            name = (
                session.get("name", "N/A") if session.get("name") else "Not populated"
            )
            username = (
                session.get("username", "N/A")
                if session.get("username")
                else "Not populated"
            )
            active = session.get("active", False)
            details_populated = session.get("details_populated", False)

            if details_populated:
                populated_count += 1
            else:
                unpopulated_count += 1

            if active:
                active_count += 1

            status = "✅" if details_populated else "⏳"

            sessions_text += f"**{i}.** {status} **ID:** `{account_id}`\n"
            sessions_text += f"   **Name:** `{name}`\n"
            sessions_text += f"   **Username:** `{username}`\n"
            sessions_text += f"   **Active:** `{active}`\n\n"

        sessions_text += f"**Total Sessions:** `{len(sessions)}` "
        sessions_text += f"(Ready: `{populated_count}`, Pending: `{unpopulated_count}`, Active: `{active_count}`)"

        await message.reply_text(sessions_text, quote=True)

    except Exception as e:
        await message.reply_text(f"Error: {str(e)}", quote=True)
