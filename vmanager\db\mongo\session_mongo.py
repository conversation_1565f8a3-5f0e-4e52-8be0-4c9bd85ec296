import asyncio
import hashlib
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import DuplicateKeyError
from vmanager import DB_URL

client = AsyncIOMotorClient(DB_URL)
db = client["vmanager"]
sessions_collection = db["sessions"]

INSERTION_LOCK = asyncio.Lock()

async def add_session(session_string: str):
    """
    Add a new session to the database with just the session string.
    Account details will be populated later.
    """
    async with INSERTION_LOCK:
        # Create a temporary ID based on a hash of the session string
        temp_id = hashlib.md5(session_string.encode()).hexdigest()[:10]
        
        # Check if the session_string already exists
        if await is_session_exists(session_string):
            # Get the existing session
            existing_session = await sessions_collection.find_one({"session_string": session_string})
            return existing_session["account_id"]
            
        # Add the new session
        await sessions_collection.insert_one({
            "session_string": session_string,
            "account_id": temp_id,  # Will be updated later with actual account ID
            "username": None,
            "name": None,
            "added_at": asyncio.get_event_loop().time(),
            "active": True,
            "details_populated": False
        })
        return temp_id

async def update_session_details(temp_id: str, account_id: str, name: str, username: str):
    """Update session details after retrieving them from Telegram"""
    async with INSERTION_LOCK:
        await sessions_collection.update_one(
            {"account_id": temp_id},
            {
                "$set": {
                    "account_id": account_id,
                    "name": name,
                    "username": username,
                    "details_populated": True
                }
            }
        )
        return True

async def delete_session(account_id: str):
    """Delete a session from the database"""
    async with INSERTION_LOCK:
        result = await sessions_collection.delete_one({"account_id": account_id})
        return result.deleted_count > 0
    
async def delete_session_by_string(session_string: str):
    """Delete a session from the database"""
    async with INSERTION_LOCK:
        result = await sessions_collection.delete_one({"session_string": session_string})
        return result.deleted_count > 0

async def get_all_sessions():
    """Get all sessions from the database"""
    cursor = sessions_collection.find()
    sessions = await cursor.to_list(length=100)
    return sessions

async def get_session(account_id: str):
    """Get a specific session by account_id"""
    session = await sessions_collection.find_one({"account_id": account_id})
    return session

async def get_session_by_string(session_string: str):
    """Get a session by its session string"""
    session = await sessions_collection.find_one({"session_string": session_string})
    return session

async def get_unpopulated_sessions():
    """Get all sessions that need their details populated"""
    cursor = sessions_collection.find({"details_populated": False, "active": True})
    sessions = await cursor.to_list(length=100)
    return sessions

async def get_session_string(account_id: str):
    """Get the session string for a given account ID"""
    session = await sessions_collection.find_one({"account_id": account_id})
    if session:
        return session.get("session_string")
    return None

async def update_session(account_id: str, name: str = None, username: str = None, session_string: str = None, active: bool = None):
    """Update session details"""
    update_data = {}
    if name is not None:
        update_data["name"] = name
    if username is not None:
        update_data["username"] = username
    if session_string is not None:
        update_data["session_string"] = session_string
    if active is not None:
        update_data["active"] = active
    
    if update_data:
        await sessions_collection.update_one(
            {"account_id": account_id},
            {"$set": update_data}
        )
        return True
    return False

async def is_session_exists(session_string: str) -> bool:
    """Check if a session exists by session string"""
    session = await sessions_collection.find_one({"session_string": session_string})
    return session is not None

async def is_account_exists(account_id: str) -> bool:
    """Check if a session exists by account ID"""
    session = await sessions_collection.find_one({"account_id": account_id})
    return session is not None

async def count_sessions():
    """Count the number of active sessions in the database"""
    count = await sessions_collection.count_documents({"active": True})
    return count

async def count_populated_sessions():
    """Count the number of populated active sessions in the database"""
    count = await sessions_collection.count_documents({"active": True, "details_populated": True})
    return count 