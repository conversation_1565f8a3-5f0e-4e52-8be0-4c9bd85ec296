# View Manager

A Telegram automation tool that manages multiple accounts for adding views and reactions to channel messages, with user authentication and role management.

## Features

- **Multiple Account Management**: Control multiple Telegram accounts simultaneously
- **User Authorization**: Control who can access the bot with admin approval system
- **Moderation Tools**: Manage moderator permissions and session access
- **View & Reaction Management**: Automatically add views and reactions to channel messages
- **Channel Management**: Join/leave channels and manage view/reaction settings per channel

## Commands

### User Management
- `/users` - List all registered users
- `/remove USER_ID` - Remove a user from the system
- `/addmod USER_ID` - Add a user as a moderator
- `/rmmod USER_ID` - Remove moderator status
- `/addsessionmod USER_ID` - Add a user as session moderator
- `/rmsessionmod USER_ID` - Remove session moderator status

### Channel Management
- `/listchannels` - List all joined channels with their status
- `/joinchannel LINK` - Join a channel with the user account
- `/leavechannel CHANNEL_ID` - Leave a channel
- `/addupview CHANNEL_ID` - Add a channel to auto-view messages
- `/rmupview CHANNEL_ID` - Remove a channel from auto-view
- `/addupreaction CHANNEL_ID EMOJI` - Add a channel to auto-react to messages
- `/rmupreaction CHANNEL_ID` - Remove a channel from auto-reactions

### Reaction & View Commands
- `/react MESSAGE_LINK EMOJI [COUNT]` - React to a message
- `/view MESSAGE_LINK [COUNT]` - View a message

### Client Management
- `/add SESSION_STRING` - Add a new Telegram session
- `/delete ACCOUNT_ID` - Delete a Telegram session
- `/listaccounts` - List all registered sessions
- `/startall` - Start all clients
- `/stopall` - Stop all clients
- `/startclient ACCOUNT_ID` - Start a specific client
- `/stopclient ACCOUNT_ID` - Stop a specific client
- `/activeclients` - Show active clients

### Moderation
- `/ban USER_ID` - Ban a user
- `/unban USER_ID` - Unban a user

### System Commands
- `/restart` - Restart the bot
- `/logs` - Get bot logs
- `/server` - Get server & bot stats
- `/stats` - Get bot user details
- `/broadcast` - Broadcast a message to all users
- `/help` - Show available commands

## Requirements

- Python 3.7+
- Pyrogram
- MongoDB
- Telegram API credentials

## Setup
```bash
# Clone this repo
git clone https://github.com/your-username/viewmanager.git

# cd folder
cd viewmanager

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
venv\Scripts\activate # For Windows
source venv/bin/activate # For Linux or MacOS

# Install Packages
pip3 install -r requirements.txt

# Copy .env.sample file & add variables
cp .env.sample .env

# Run bot
python3 -m vmanager
```

## Environment Variables
```
APP_ID=  # Telegram API ID
API_HASH=  # Telegram API Hash
BOT_TOKEN=  # Telegram Bot Token
SESSION_STRING=  # Telegram User session string
MONGO_URL=  # MongoDB connection string
ADMINS=  # Comma-separated list of admin user IDs
```
