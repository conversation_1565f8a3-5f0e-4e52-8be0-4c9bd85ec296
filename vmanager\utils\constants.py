from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton


START_KB = InlineKeyboardMarkup(
    [
        [
            InlineKeyboardButton("🆘 Help", callback_data="help_cb"),
            InlineKeyboardButton("👨‍💻 Developer", url="https://t.me/jithumon"),
        ],
        [InlineKeyboardButton("📢 Update Channel", url="https://t.me/ELUpdates")],
    ]
)


HELP_KB = InlineKeyboardMarkup(
    [
        [
            InlineKeyboardButton("🫂 About", callback_data="about"),
        ],
        [InlineKeyboardButton("🥷 Admin Commands", callback_data="admin_cb")],
        [InlineKeyboardButton("🔙 Back", callback_data="start_cb")],
    ]
)

HELP_RET_KB = InlineKeyboardMarkup(
    [[InlineKeyboardButton("🔙 Back", callback_data="help_cb")]]
)

PRO_KB = InlineKeyboardMarkup(
    [
        [
            InlineKeyboardButton("⭐ 80", callback_data="pro_stars"),
        ],
        [
            InlineKeyboardButton("₹ 100", callback_data="pro_rupees"),
        ],
        [
            InlineKeyboardButton("$ 1.99", callback_data="pro_dollar"),
        ],
        [InlineKeyboardButton("🔙 Back", callback_data="back_m")],
    ]
)

PRO_RUPEE_KB = InlineKeyboardMarkup(
    [
        [InlineKeyboardButton("PAY NOW", url="https://rzp.io/l/LLq2OcZ")],
        [InlineKeyboardButton("🔙 Back", callback_data="pro")],
    ]
)

PRO_DOLLAR_KB = InlineKeyboardMarkup(
    [
        [InlineKeyboardButton("PAY NOW", url="https://paypal.me/jithumon")],
        [InlineKeyboardButton("🔙 Back", callback_data="pro")],
    ]
)


STARTMSG = "Hi **[{}](tg://user?id={})**, I am a premium Auto Message Wiper Bot, powered by @ELUpdates.\n\n __You must buy a PRO plan in order to use this bot in groups. More details can be found in /help.__"

HELPMSG = """
**You can find the bot commands here.**
/connect - __Connect a chat id__ - `/connect {chat id}`
__(Bot must be admin in the group with delete & invite permissions)__
/setdur - __Set auto wipe duration for connected group__ - `/setdur {duration in seconds}`
eg: /setdur 120 for 2 minutes
/pro - __To know about & upgrade to PRO plan.__
/start - __See start menu.__ (in private)
/start - __Invite assistant bot.__ (in group)
/help - __See help menu.__
"""

ADM_HELPMSG = """
/addchat - Add a chat to pro plan - `/addchat chat_id`
/delchat - Remove a user from pro plan - `/delchat chat_id`
/delmsg - Manually delete messages from groups - `/delmsg chat_id msg_id` or `/delmsg chat_id first_msg_id last_msg_id`
/deldbmsg - Manually delete messages in DB from groups - `/delmsg chat_id`
/forcesub - __Set force subscribe channel.__ - `/forcesub channel_id` 
__- Bot must be admin of that channel (Bot will create a new invite link for that channel).__
__- Send `/forcesub off` to disable force subscribe__
/checklink - __Check invite link for force subscribe channel.__
/restart - Restart the bot.
/logs - Get bot logs.
/server - Get sever & bot stats.
/stats - Get bot user details.

(Owner only)
/broadcast - Reply to a message to broadcast it to users.
"""

PROMSG = "Hi, below are the details of PRO plan.\n\n>Price/30 days:-\n>⭐ - 80\n>₹ - 100\n>$ - 1.99"
PRO_STAR_MSG = "Please pay 80 stars to upgrade to PRO plan."
PRO_RUPEE_MSG = "Please pay ₹100 with below link to upgrade to PRO plan.\nContact @CoderELAlpha for UPI."
PRO_DOLLAR_MSG = "Please pay $1.99 with below link to upgrade to PRO plan."


ABT_MSG = """
**About This Bot** 

A Telegram Bot to auto delete messages in groups/channels.

Source Code : [Private](https://github.com/EL-Coders/elvmanager)
Framework : [Pyrogram](https://docs.pyrogram.org)
Language : [Python](https://www.python.org)
Maintainer : [Jɪᴛʜᴜ Mᴀᴛʜᴇᴡ Jᴏsᴇᴘʜ](https://t.me/jithumon)
"""


DEF_REC = [
    "👍", "👎", "❤", "🔥", "🥰", "👏", "😁", "🤔", "🤯", "😱", 
    "🤬", "😢", "🎉", "🤩", "🤮", "💩", "🙏", "👌", "🕊", "🤡", 
    "🥱", "🥴", "😍", "🐳", "❤‍🔥", "🌚", "🌭", "💯", "🤣", "⚡", 
    "🍌", "🏆", "💔", "🤨", "😐", "🍓", "🍾", "💋", "🖕", "😈", 
    "😴", "😭", "🤓", "👻", "👨‍💻", "👀", "🎃", "🙈", "😇", "😨", 
    "🤝", "✍", "🤗", "🫡", "🎅", "🎄", "☃", "💅", "🤪", "🗿", 
    "🆒", "💘", "🙉", "🦄", "😘", "💊", "🙊", "😎", "👾", "🤷‍♂", 
    "🤷", "🤷‍♀", "😡"
    ]