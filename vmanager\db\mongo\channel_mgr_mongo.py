import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import DuplicateKeyError
from vmanager import DB_URL


client = AsyncIOMotorClient(DB_URL)
db = client["vmanager"]
channels_collection = db["channelmanagement"]

INSERTION_LOCK = asyncio.Lock()


async def add_channel(channel_id: int, channel_name: str, user_id: int):
    await channels_collection.update_one(
        {"channel_id": channel_id},
        {
            "$set": {
                "channel_name": channel_name,
                "channel_id": channel_id,
                "user_id": user_id,
                "joined_at": asyncio.get_event_loop().time(),
                "upcoming_views": False,
                "upcoming_reactions": False,
                "reactions": [],
                "reac_count": 0,
                "view_count": 0,
            }
        },
        upsert=True,
    )


async def remove_channel(channel_id: int):
    result = await channels_collection.delete_one({"channel_id": channel_id})
    return result.deleted_count > 0


async def get_all_channels():
    cursor = channels_collection.find()
    channels = await cursor.to_list()
    return channels


async def get_channels_by_user(user_id: int):
    cursor = channels_collection.find({"user_id": user_id})
    channels = await cursor.to_list()
    return [channel["channel_id"] for channel in channels]


async def get_all_channel_ids():
    cursor = channels_collection.find()
    channels = await cursor.to_list()
    return [channel["channel_id"] for channel in channels]


async def get_upcoming_views(channel_id: int):
    cursor = channels_collection.find({"channel_id": channel_id})
    channels = await cursor.to_list(length=1)
    if not channels:
        return False
    return {
        "upcoming_views": channels[0]["upcoming_views"],
        "view_count": channels[0].get("view_count", 0),
    }


async def get_upcoming_reactions(channel_id: int):
    cursor = channels_collection.find({"channel_id": channel_id})
    channels = await cursor.to_list(length=1)
    if not channels:
        return False
    return {
        "reactions": [reaction for reaction in channels[0]["reactions"]],
        "reac_count": channels[0].get("reac_count", 0),
    }


async def add_upcoming_view(channel_id: int, count: int):
    await channels_collection.update_one(
        {"channel_id": channel_id},
        {"$set": {"upcoming_views": True, "view_count": count}},
    )
    return True


async def remove_upcoming_view(channel_id: int):
    await channels_collection.update_one(
        {"channel_id": channel_id},
        {"$set": {"upcoming_views": False}},
    )
    return True


async def add_upcoming_reaction(channel_id: int, reaction: list, count: int):
    await channels_collection.update_one(
        {"channel_id": channel_id},
        {"$set": {"upcoming_reactions": True, "reactions": reaction, "reac_count": count}},
    )
    return True


async def remove_upcoming_reaction(channel_id: int):
    await channels_collection.update_one(
        {"channel_id": channel_id},
        {"$set": {"upcoming_reactions": False}},
    )