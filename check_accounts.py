#!/usr/bin/env python3
"""
<PERSON>ript to check how many Telegram accounts are stored in the View Manager bot database.
"""

import os
from dotenv import load_dotenv
from pymongo import MongoClient

# Load environment variables
load_dotenv()

def check_account_count():
    """Check and display the number of stored Telegram accounts."""
    try:
        # Get MongoDB URL from environment
        mongo_url = os.getenv('MONGO_DB_URL')
        if not mongo_url:
            print("❌ MONGO_DB_URL not found in .env file!")
            return

        print("🔍 Connecting to MongoDB...")

        # Connect to MongoDB
        client = MongoClient(mongo_url)
        db = client["vmanager"]
        sessions_collection = db["sessions"]

        print("� Checking Telegram accounts in database...")
        print("=" * 50)

        # Get all sessions
        all_sessions = list(sessions_collection.find())
        total_sessions = len(all_sessions)

        # Count different types
        populated_count = 0
        unpopulated_count = 0
        inactive_count = 0
        active_count = 0

        for session in all_sessions:
            is_active = session.get("active", True)
            is_populated = session.get("details_populated", False)

            if is_active:
                active_count += 1
                if is_populated:
                    populated_count += 1
                else:
                    unpopulated_count += 1
            else:
                inactive_count += 1

        print(f"📈 Total Sessions (All): {total_sessions}")
        print(f"🟢 Active Sessions: {active_count}")
        print(f"✅ Ready Sessions (Populated): {populated_count}")
        print(f"⏳ Pending Sessions (Unpopulated): {unpopulated_count}")
        print(f"❌ Inactive Sessions: {inactive_count}")

        print("\n" + "=" * 50)

        if populated_count > 0:
            print("📋 Account Details:")
            print("-" * 30)

            ready_sessions = [s for s in all_sessions if s.get("details_populated", False) and s.get("active", True)]

            for i, session in enumerate(ready_sessions[:10], 1):  # Show first 10
                account_id = session.get("account_id", "N/A")
                name = session.get("name", "N/A")
                username = session.get("username", "N/A")

                print(f"{i}. ID: {account_id}")
                print(f"   Name: {name}")
                print(f"   Username: @{username if username != 'N/A' else 'Not available'}")
                print()

            if len(ready_sessions) > 10:
                print(f"... and {len(ready_sessions) - 10} more accounts")

        else:
            print("ℹ️  No ready accounts found.")
            if unpopulated_count > 0:
                print(f"   📝 You have {unpopulated_count} sessions that need details populated.")
                print("   💡 The bot will automatically populate details when it starts.")
            else:
                print("   💡 You may need to add Telegram session strings using /add command in the bot.")

        # Close connection
        client.close()

    except Exception as e:
        print(f"❌ Error checking accounts: {e}")
        print("Make sure your MongoDB connection is working and the credentials are correct.")

def main():
    """Main function to run the account check."""
    print("🤖 View Manager - Account Counter")
    print("=" * 50)

    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please create a .env file with your database configuration.")
        return

    # Check if MONGO_DB_URL is set
    mongo_url = os.getenv('MONGO_DB_URL')
    if not mongo_url:
        print("❌ MONGO_DB_URL not found in .env file!")
        print("Please add your MongoDB connection string to the .env file.")
        return

    print(f"🔗 Database URL: {mongo_url[:20]}...")
    print()

    # Run the function
    try:
        check_account_count()
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
