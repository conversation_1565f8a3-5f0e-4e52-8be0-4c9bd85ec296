from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from vmanager import ADMINS
from vmanager.db import (
    is_command_authorized,
    authorize_command,
    revoke_command,
    get_user_commands,
    is_user_authorized,
)


async def check_command_auth(client: Client, message: Message, command: str) -> bool:
    user_id = message.from_user.id
    if user_id in ADMINS:
        return True

    if await is_command_authorized(user_id, command):
        return True

    await send_command_request(client, message, command)
    return False


async def send_command_request(client: Client, message: Message, command: str):
    user = message.from_user
    request_text = (
        f"🔐 Command Authorization Request\n\n"
        f"User: {user.mention} (`{user.id}`)\n"
        f"Command: `/{command}`\n"
        f"Message: {message.text}"
    )

    keyboard = InlineKeyboardMarkup(
        [
            [
                InlineKeyboardButton(
                    "✅ Approve", callback_data=f"cmd_auth_approve_{user.id}_{command}"
                ),
                InlineKeyboardButton(
                    "❌ Deny", callback_data=f"cmd_auth_deny_{user.id}_{command}"
                ),
            ]
        ]
    )

    for admin_id in ADMINS:
        try:
            await client.send_message(admin_id, request_text, reply_markup=keyboard)
        except Exception as e:
            print(f"Failed to send command request to admin {admin_id}: {e}")

    await message.reply_text(
        "⏳ Your command request has been sent to administrators for approval. "
        "You will be notified once it's approved."
    )


@Client.on_callback_query(filters.regex("^cmd_auth_approve_"))
async def handle_command_approval(client: Client, callback_query):
    _, user_id, command = callback_query.data.split("_")[2:]
    user_id = int(user_id)

    success = await authorize_command(user_id, command)

    if success:
        await callback_query.answer("Command authorized successfully!")
        await callback_query.message.edit_text(
            f"{callback_query.message.text}\n\n✅ Approved by {callback_query.from_user.mention}"
        )

        try:
            await client.send_message(
                user_id,
                f"✅ Your request to use the `/{command}` command has been approved!",
            )
        except Exception as e:
            print(f"Failed to notify user {user_id}: {e}")
    else:
        await callback_query.answer("Failed to authorize command!")


@Client.on_callback_query(filters.regex("^cmd_auth_deny_"))
async def handle_command_denial(client: Client, callback_query):
    _, user_id, command = callback_query.data.split("_")[2:]
    user_id = int(user_id)

    await callback_query.answer("Command request denied!")
    await callback_query.message.edit_text(
        f"{callback_query.message.text}\n\n❌ Denied by {callback_query.from_user.mention}"
    )

    try:
        await client.send_message(
            user_id, f"❌ Your request to use the `/{command}` command has been denied."
        )
    except Exception as e:
        print(f"Failed to notify user {user_id}: {e}")


@Client.on_message(filters.command("mycommands") & filters.private)
async def show_my_commands(client: Client, message: Message):
    user_id = message.from_user.id
    commands = await get_user_commands(user_id)

    if not commands:
        await message.reply_text("You don't have any authorized commands yet.")
        return

    text = "🔑 Your Authorized Commands:\n\n"
    for cmd, status in commands.items():
        status_emoji = "✅" if status else "❌"
        text += f"{status_emoji} /{cmd}\n"

    await message.reply_text(text)
