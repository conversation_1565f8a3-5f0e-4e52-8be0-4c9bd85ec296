import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import DuplicateKeyError
from vmanager import DB_URL

# MongoDB setup
client = AsyncIOMotorClient(DB_URL)
db = client["vmanager"]
broadcast_collection = db["broadcast"]

INSERTION_LOCK = asyncio.Lock()


async def add_user(user_id, user_name):
    async with INSERTION_LOCK:
        try:
            await broadcast_collection.insert_one(
                {"user_id": user_id, "user_name": user_name}
            )
        except DuplicateKeyError:
            pass


async def is_user(user_id):
    async with INSERTION_LOCK:
        result = await broadcast_collection.find_one({"user_id": user_id})
        return result is not None


async def get_user():
    async with INSERTION_LOCK:
        cursor = broadcast_collection.find({}, {"_id": 0, "user_id": 1}).sort(
            "user_id", 1
        )
        result = await cursor.to_list(length=None)
        return [doc["user_id"] for doc in result]


async def del_user(user_id):
    async with INSERTION_LOCK:
        await broadcast_collection.delete_one({"user_id": user_id})
